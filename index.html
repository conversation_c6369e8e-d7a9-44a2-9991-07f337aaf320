<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简历 - 张伟</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js" integrity="sha512-BNaRQnYJYiPSqHHDb58B0yaPfCu+Wgds8Gp/gU33kqBtgNS4tSPHuGibyoeqMV/TJlSKda6FXzoEyYGjTe+vXA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js" integrity="sha512-qZvrmS2ekKPF2mSznTQsxqPgnpkI4DNTlrdUmTzrDgektczlKNRRhy5X5AAOnx5S09ydFYWWNSfcEqDTTHgtNA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <style>
        body {
            font-family: 'Lato', 'Microsoft YaHei', sans-serif;
            line-height: 1.7;
            color: #444;
            background-color: #f7f7f7;
            margin: 0;
            padding: 20px 0;
            display: flex;
            justify-content: center;
        }
        .container {
            width: 800px;
            max-width: 95%;
            background-color: #fff;
            margin: 20px;
            padding: 40px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            display: flex;
            flex-direction: column;
            border-radius: 6px;
        }
        .profile-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding-bottom: 15px;
            position: relative;
        }
        .profile-header .text-info {
            flex: 1;
            margin-left: 30px;
        }
        .profile-header .name {
            margin: 0 0 12px 0;
            font-size: 32px;
            font-weight: 700;
            color: #333;
            position: relative;
            display: inline-block;
        }
        .profile-header .title {
            margin: 0;
            color: #555;
            font-size: 16px;
            line-height: 1.6;
        }
        .profile-header .title .job-label {
            font-weight: 600;
            color: #555;
            margin-right: 4px;
        }
        .profile-header .title span.jobs {
            color: #2c5282;
            font-weight: 600;
            display: block;
            margin-top: 5px;
        }
        .profile-pic {
            width: 120px;
            height: 150px;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            flex-shrink: 0;
            background-color: #f0f0f0;
        }
        .profile-pic img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }
        .main-content {
            width: 100%;
        }
        .left-column {
            width: 100%;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #2c5282;
            border-bottom: 2px solid #2c5282;
            padding-bottom: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .section-title i {
            margin-right: 10px;
            font-size: 22px;
        }
        .job-item, .edu-item {
            margin-bottom: 20px;
        }
        .job-item h4, .edu-item h4 {
            margin: 0 0 5px 0;
            font-size: 17px;
            color: #333;
            display: flex;
            justify-content: space-between;
            font-weight: 700;
        }
        .job-item .date, .edu-item .date {
            font-size: 14px;
            color: #777;
            font-weight: normal;
            white-space: nowrap;
            margin-left: 15px;
        }
        .job-item .company, .edu-item .school {
            font-weight: bold;
        }
        .job-item .title, .edu-item .major {
             font-style: normal;
             color: #666;
             font-size: 15px;
             margin-bottom: 8px;
        }
        .job-item ul, .edu-item ul {
            list-style: disc;
            margin-left: 20px;
            padding-left: 5px;
            font-size: 15px;
            color: #555;
        }
         .job-item ul li, .edu-item ul li {
            margin-bottom: 5px;
         }
        .skills ul {
            list-style: none;
            padding-left: 0;
            font-size: 15px;
        }
        .skills li {
            margin-bottom: 15px;
            line-height: 1.8;
        }
        .skills strong {
            display: block;
            font-weight: 700;
            color: #444;
            margin-bottom: 5px;
            font-size: 16px;
        }
        .skills br {
            display: block;
            content: "";
            margin-top: 5px;
        }
        .self-evaluation p {
            font-size: 15px;
            color: #555;
            text-indent: 0;
            line-height: 1.8;
        }
        .contact-info .info-grid {
             display: grid;
             grid-template-columns: repeat(2, 1fr);
             gap: 15px 20px;
        }
        .contact-info p {
            margin: 0;
            font-size: 15px;
        }
        .contact-info strong {
            display: inline-block;
            width: auto;
            color: #555;
            margin-right: 5px;
            font-weight: 700;
        }
        @media (max-width: 768px) {
            body {
                padding: 0;
            }
            .container {
                width: 100%;
                margin: 0;
                padding: 20px;
                box-shadow: none;
                border-radius: 0;
            }
            .profile-header {
                flex-direction: column;
                align-items: center;
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 10px;
            }
             .profile-pic {
                 margin-bottom: 15px;
             }
            .profile-header .text-info {
                width: 100%;
                margin-left: 0;
            }
            .main-content {
                /* display: flex;
                flex-direction: column; */
                /* 移除或调整不再需要的媒体查询规则 */
                /*
                .main-content {
                    flex-direction: column;
                }
                .right-column {
                    border-left: none;
                    padding-left: 0;
                    margin-top: 20px;
                }
                .left-column {
                    padding-right: 0;
                }
                */
            }
            .profile-header .name {
                font-size: 26px;
            }
            .section-title {
                font-size: 18px;
            }
            .contact-info .info-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            .job-item h4, .edu-item h4 {
                flex-direction: column;
                align-items: flex-start;
            }
            .job-item .date, .edu-item .date {
                margin-left: 0;
                margin-top: 3px;
                font-size: 13px;
            }
        }
        .download-section {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-bottom: 25px;
        }
        .download-button {
            display: inline-block;
            padding: 8px 18px;
            background-color: #2c5282;
            color: #fff;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .download-button:hover {
            background-color: #1a365d;
            color: #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .download-button i {
            margin-right: 6px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="profile-header">
             <div class="profile-pic">
                 <img src="1.png" alt="个人照片" onerror="this.src='data:image/svg+xml,%3Csvg xmlns=\\\'http://www.w3.org/2000/svg\\\' width=\\\'120\\\' height=\\\'150\\\' viewBox=\\\'0 0 120 150\\\'%3E%3Crect width=\\\'120\\\' height=\\\'150\\\' fill=\\\'%23f0f0f0\\\'/%3E%3Ctext x=\\\'60\\\' y=\\\'75\\\' dominant-baseline=\\\'middle\\\' text-anchor=\\\'middle\\\' font-family=\\\'Arial\\\' font-size=\\\'14\\\' fill=\\\'%23666\\\' stroke=\\\'%23666\\\' stroke-width=\\\'0.5\\\' %3E照片%3C/text%3E%3C/svg%3E';" style="width: 100%; height: 100%; object-fit: cover;">
             </div>
             <div class="text-info">
                 <h1 class="name">张伟</h1>
                 <p class="title">
                     <span class="job-label">求职意向:</span>
                     <span class="jobs">新能源汽车销售顾问 / 汽车销售经理 / 客户关系专员</span>
                 </p>
             </div>
        </div>

        <div class="download-section">
            <a href="1.png" download="张伟_简历照片.png" class="download-button">
                <i class="bi bi-image"></i> 下载照片
            </a>
            <button id="downloadImageButton" class="download-button">
                <i class="bi bi-file-earmark-image"></i> 下载简历 (图片)
            </button>
            <button id="downloadPdfButton" class="download-button">
                <i class="bi bi-file-earmark-pdf"></i> 下载简历 (PDF)
            </button>
        </div>

        <div class="main-content">
            <div class="left-column">
                <div class="section skills">
                    <h3 class="section-title"><i class="bi bi-tools"></i>核心技能</h3>
                    <ul>
                        <li><strong>销售技能:</strong>
                             精通新能源汽车产品知识，深度了解特斯拉、小米汽车等品牌技术特点; <br>
                             熟练掌握汽车销售流程，从客户接待到交车全流程服务; <br>
                             具备优秀的客户沟通能力和需求分析能力; <br>
                             熟悉汽车金融产品，能为客户提供专业的购车方案; <br>
                             掌握CRM系统操作，擅长客户关系维护和跟进; <br>
                             了解新能源汽车充电设施和使用场景; <br>
                             具备团队协作精神和目标导向的工作态度; <br>
                             熟练使用办公软件和销售管理系统; <br>
                             具备出色的抗压能力和学习适应能力; <br>
                        </li>
                        <li><strong>行业认知:</strong>
                             深度关注新能源汽车行业发展趋势，了解智能驾驶、车联网等前沿技术
                        </li>
                    </ul>
                </div>

                <div class="section">
                    <h3 class="section-title"><i class="bi bi-briefcase"></i>工作经历</h3>
                    <div class="job-item">
                        <h4>
                            <span class="company">贵阳新能源汽车销售中心</span>
                            <span class="date">2022.07 - 2023.07 </span>
                        </h4>
                        <p class="title">汽车销售顾问</p>
                        <ul>
                            <li>负责新能源汽车销售工作，月均销售量15台，连续6个月超额完成销售目标。</li>
                            <li>深度学习新能源汽车产品知识，为客户提供专业的产品介绍和试驾服务。</li>
                            <li>维护客户关系，建立完善的客户档案，客户满意度达95%以上。</li>
                            <li>协助客户办理购车手续，包括贷款申请、保险购买、上牌等全流程服务。</li>
                            <li>参与展厅活动策划和执行，提升品牌知名度和客户到店率。</li>
                            <li>熟练使用CRM系统进行客户管理和销售数据分析。</li>
                            <li>作为团队骨干，协助新员工培训，分享销售经验和技巧。</li>
                        </ul>
                    </div>
                </div>

                <div class="section">
                    <h3 class="section-title"><i class="bi bi-trophy"></i>销售业绩</h3>
                    <div class="job-item">
                        <h4>
                            <span class="company">新能源汽车销售冠军</span>
                            <span class="date">2024.09 - 2025.03</span>
                        </h4>
                        <p class="title">销售业绩突出</p>
                        <ul>
                            <li>连续6个月获得销售冠军，月均销售额超过200万元</li>
                            <li>成功开发大客户资源，建立长期合作关系</li>
                            <li>客户转介绍率达到30%，展现优秀的客户服务能力</li>
                            <li>参与新车型上市推广活动，协助制定销售策略</li>
                            <li>培训新员工销售技巧，提升团队整体业绩</li>
                        </ul>
                    </div>
                    <div class="job-item">
                        <h4>
                            <span class="company">特斯拉车主俱乐部活动组织</span>
                            <span class="date">2024.09 - 2025.4</span>
                        </h4>
                        <p class="title">客户关系维护专员</p>
                        <ul>
                            <li>组织策划车主聚会活动，增强品牌忠诚度</li>
                            <li>建立车主微信群，提供用车咨询和技术支持</li>
                            <li>收集客户反馈，协助改进销售和售后服务流程</li>
                            <li>开展新能源汽车知识普及活动，提升品牌影响力</li>
                            <li>维护超过500名车主的长期关系，促进二次购车和转介绍</li>
                        </ul>

                    <div class="project-gallery">
                        <div class="row" style="display: flex; flex-wrap: wrap; justify-content: space-around; margin-top: 15px;">
                            <div class="project-item" style="width: 30%; margin: 0 1%; text-align: center;">
                                <h4>
                                    <span class="company" style="color: #2c5282;">特斯拉销售认证</span>
                                </h4>
                                <img src="人工智能编程.png" alt="特斯拉销售认证" class="project-image" style="max-width: 100%; height: 120px; object-fit: contain;">
                            </div>
                            <div class="project-item" style="width: 30%; margin: 0 1%; text-align: center;">
                                <h4>
                                    <span class="company" style="color: #2c5282;">新能源车展</span>
                                </h4>
                                <img src="缘分黔城.jpg" alt="新能源车展活动" class="project-image" style="max-width: 100%; height: 120px; object-fit: contain;">
                            </div>
                            <div class="project-item" style="width: 30%; margin: 0 1%; text-align: center;">
                                <h4>
                                    <span class="company" style="color: #2c5282;">客户服务奖</span>
                                </h4>
                                <img src="智叶科技服务.jpg" alt="客户服务优秀奖" class="project-image" style="max-width: 100%; height: 120px; object-fit: contain;">
                            </div>
                        </div>
                    </div>
                    <p class="project-note" style="text-align: center; margin-top: 10px;">以上均为销售工作中获得的认证和荣誉</p>
                    </div>
                </div>

                <div class="section self-evaluation">
                    <h3 class="section-title"><i class="bi bi-person-check"></i>自我评价</h3>
                    <p>
                        具备出色的沟通能力和团队协作精神，能够清晰阐述问题并有效利用 AI 工具辅助解决问题。善于沟通协作，有较强的分析能力和学习能力，能够快速掌握新技术与新工具；具备良好的代码规范和文档编写习惯；认真负责，吃苦耐劳。
                    </p>
                </div>

                <div class="section">
                    <h3 class="section-title"><i class="bi bi-mortarboard"></i>教育背景</h3>
                    <div class="edu-item">
                        <h4>
                            <span class="school">长春理工大学(一本)</span>
                            <span class="date">2018.09 - 2022.06</span>
                        </h4>
                        <p class="major">电子信息科学与技术 </p>
                        <ul>
                            <li>主修课程：数据结构、操作系统、计算机网络、数据库原理、软件工程等。</li>
                            <li>在校活动：参与校内编程竞赛，加入技术社团，参与开发校园信息发布平台项目。</li>                        </ul>
                    </div>
                </div>

                <div class="section contact-info">
                    <h3 class="section-title"><i class="bi bi-person-vcard"></i>联系方式</h3>
                    <div class="info-grid">
                        <p><strong>电话:</strong> 17885708117</p>
                        <p><strong>邮箱:</strong> <EMAIL></p>
                        <p><strong>现居:</strong> 贵阳</p>
                        <p><strong>生日:</strong> 1999.07.01</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 预处理所有图片，将跨域图片转换为base64格式
        function preprocessImages(container) {
            console.log('[preprocessImages] Starting for container:', container.classList[0] || container.id || 'anonymous container');
            return new Promise((resolve) => {
                const images = container.querySelectorAll('img');
                let processedCount = 0;

                if (images.length === 0) {
                    console.log('[preprocessImages] No images found in container.');
                    resolve();
                    return;
                }
                console.log(`[preprocessImages] Found ${images.length} images to process.`);

                images.forEach((img, index) => {
                    const imgSrcForLog = img.src.length > 100 ? img.src.substring(0, 97) + '...' : img.src;
                    console.log(`[preprocessImages] Image ${index + 1}/${images.length}: Original src = ${imgSrcForLog}`);
                    
                    if (img.src.startsWith('data:') || img.src.indexOf('svg+xml') > -1) {
                        console.log(`[preprocessImages] Image ${index + 1} is already data/SVG. Skipping.`);
                        processedCount++;
                        if (processedCount === images.length) {
                            console.log('[preprocessImages] All images accounted for (all were data/SVG or processed). Resolving promise.');
                            resolve();
                        }
                        return;
                    }

                    const newImg = new Image();
                    newImg.crossOrigin = 'Anonymous'; // Standard casing
                    
                    newImg.onload = function() {
                        const newImgSrcForLog = newImg.src.length > 100 ? newImg.src.substring(0, 97) + '...' : newImg.src;
                        console.log(`[preprocessImages] Image ${index + 1} (${newImgSrcForLog}) loaded successfully via new Image(). Natural WxH: ${newImg.naturalWidth}x${newImg.naturalHeight}`);
                        try {
                            const canvas = document.createElement('canvas');
                            canvas.width = newImg.naturalWidth || newImg.width;
                            canvas.height = newImg.naturalHeight || newImg.height;
                            if (canvas.width === 0 || canvas.height === 0) {
                                console.warn(`[preprocessImages] Image ${index + 1} (${newImgSrcForLog}) has zero dimensions. Using 100x100 default for canvas.`);
                                canvas.width = canvas.width || 100;
                                canvas.height = canvas.height || 100;
                            }
                            const ctx = canvas.getContext('2d');
                            ctx.drawImage(newImg, 0, 0, canvas.width, canvas.height);
                            const dataURL = canvas.toDataURL('image/png');
                            console.log(`[preprocessImages] Image ${index + 1} converted to dataURL. Length: ${dataURL.length}. Snippet: ${dataURL.substring(0, 60)}...`);
                            img.src = dataURL;
                        } catch (e) {
                            console.warn(`[preprocessImages] Image ${index + 1} (${newImgSrcForLog}): Failed to convert to base64. Error:`, e.message);
                            setPlaceholderImage(img, index, newImgSrcForLog, 'conversion_error');
                        }
                        processedCount++;
                        if (processedCount === images.length) {
                            console.log('[preprocessImages] All images processed. Resolving promise.');
                            resolve();
                        }
                    };
                    
                    newImg.onerror = function() {
                        const newImgSrcForLog = newImg.src.length > 100 ? newImg.src.substring(0, 97) + '...' : newImg.src;
                        console.warn(`[preprocessImages] Image ${index + 1} (${newImgSrcForLog}) failed to load via new Image().`);
                        setPlaceholderImage(img, index, newImgSrcForLog, 'load_error');
                        processedCount++;
                        if (processedCount === images.length) {
                            console.log('[preprocessImages] All images processed (some with errors). Resolving promise.');
                            resolve();
                        }
                    };
                    
                    let quantifiableSrc = img.getAttribute('src'); // Use getAttribute to get the original value if needed
                    if (!quantifiableSrc) {
                        console.warn(`[preprocessImages] Image ${index + 1} has no src attribute. Setting placeholder.`);
                        setPlaceholderImage(img, index, 'no_src_attr', 'no_src_attr_error');
                        processedCount++;
                         if (processedCount === images.length) {
                            console.log('[preprocessImages] All images processed (some with no src). Resolving promise.');
                            resolve();
                        }
                        return; // Skip newImg.src assignment if original src is missing
                    }
                    newImg.src = quantifiableSrc; 
                    console.log(`[preprocessImages] Image ${index + 1}: Assigned src to newImg: ${quantifiableSrc}`);
                });
            });
        }

        function setPlaceholderImage(imgElement, index, originalSrc, errorType) {
            try {
                const placeholderCanvas = document.createElement('canvas');
                // Attempt to get original dimensions if available, otherwise default
                placeholderCanvas.width = imgElement.width || 100;
                placeholderCanvas.height = imgElement.height || 100;
                if (placeholderCanvas.width === 0) placeholderCanvas.width = 100;
                if (placeholderCanvas.height === 0) placeholderCanvas.height = 100;

                const pctx = placeholderCanvas.getContext('2d');
                pctx.fillStyle = '#f0f0f0';
                pctx.fillRect(0, 0, placeholderCanvas.width, placeholderCanvas.height);
                pctx.fillStyle = '#666';
                pctx.font = '12px Arial';
                pctx.textAlign = 'center';
                pctx.textBaseline = 'middle';
                pctx.fillText(imgElement.alt || `图片 ${index + 1}`, placeholderCanvas.width / 2, placeholderCanvas.height / 2);
                pctx.fillText(`(${errorType})`, placeholderCanvas.width / 2, placeholderCanvas.height / 2 + 15);
                const placeholderDataURL = placeholderCanvas.toDataURL();
                imgElement.src = placeholderDataURL;
                console.log(`[setPlaceholderImage] Image ${index + 1} (${originalSrc}): Set to placeholder due to ${errorType}. Placeholder Length: ${placeholderDataURL.length}`);
            } catch (e) {
                console.error(`[setPlaceholderImage] Image ${index + 1} (${originalSrc}): Failed to create placeholder. Error:`, e.message);
                // As a last resort, set to a very simple broken image data URI or leave src as is (which might be broken)
                imgElement.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'; // Transparent 1x1 GIF
            }
        }

        document.getElementById('downloadImageButton').addEventListener('click', function() {
            const resumeContainer = document.querySelector('.container');
            const button = this;
            
            // 隐藏下载按钮区域
            const downloadSection = document.querySelector('.download-section');
            if (downloadSection) downloadSection.style.display = 'none';
            
            // 禁用按钮并显示加载状态
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';
            
            // 创建状态指示器
            const statusDiv = document.createElement('div');
            statusDiv.style.position = 'fixed';
            statusDiv.style.bottom = '20px';
            statusDiv.style.left = '20px';
            statusDiv.style.padding = '10px';
            statusDiv.style.backgroundColor = 'rgba(0,0,0,0.7)';
            statusDiv.style.color = 'white';
            statusDiv.style.borderRadius = '5px';
            statusDiv.style.zIndex = '9999';
            statusDiv.textContent = '正在准备图片...';
            document.body.appendChild(statusDiv);
            
            // 创建一个深度克隆的容器用于截图
            const clonedContainer = resumeContainer.cloneNode(true);
            clonedContainer.style.position = 'absolute';
            clonedContainer.style.left = '-9999px';
            clonedContainer.style.top = '0';
            clonedContainer.style.width = resumeContainer.offsetWidth + 'px';
            clonedContainer.style.background = '#fff';
            document.body.appendChild(clonedContainer);
            
            // 隐藏克隆容器中的下载按钮
            const clonedDownloadSection = clonedContainer.querySelector('.download-section');
            if (clonedDownloadSection) clonedDownloadSection.style.display = 'none';
            
            // 预处理克隆容器中的所有图片
            const images = clonedContainer.querySelectorAll('img');
            let loadedCount = 0;
            const totalImages = images.length;
            
            // 如果没有图片，直接进行截图
            if (totalImages === 0) {
                captureAndDownload();
                return;
            }
            
            // 处理每个图片
            images.forEach((img, index) => {
                // 如果图片已经是data URI，直接计数
                if (img.src.startsWith('data:')) {
                    loadedCount++;
                    statusDiv.textContent = `加载图片中 (${loadedCount}/${totalImages})...`;
                    if (loadedCount === totalImages) captureAndDownload();
                    return;
                }
                
                // 创建新图片对象
                const newImg = new Image();
                newImg.crossOrigin = 'Anonymous';
                
                newImg.onload = function() {
                    try {
                        // 将图片转换为base64
                        const canvas = document.createElement('canvas');
                        canvas.width = newImg.naturalWidth || 100;
                        canvas.height = newImg.naturalHeight || 100;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(newImg, 0, 0);
                        const dataURL = canvas.toDataURL('image/png');
                        img.src = dataURL; // 替换原图片src
                    } catch (e) {
                        console.error('图片处理失败:', e);
                        // 创建占位图
                        createPlaceholder(img);
                    }
                    
                    loadedCount++;
                    statusDiv.textContent = `加载图片中 (${loadedCount}/${totalImages})...`;
                    if (loadedCount === totalImages) captureAndDownload();
                };
                
                newImg.onerror = function() {
                    console.error('图片加载失败:', img.src);
                    createPlaceholder(img);
                    loadedCount++;
                    statusDiv.textContent = `加载图片中 (${loadedCount}/${totalImages})...`;
                    if (loadedCount === totalImages) captureAndDownload();
                };
                
                // 确保图片路径是绝对路径
                if (img.src.startsWith('http') || img.src.startsWith('data:')) {
                    newImg.src = img.src;
                } else {
                    // 处理相对路径
                    const a = document.createElement('a');
                    a.href = img.src;
                    newImg.src = a.href; // 转换为绝对路径
                }
            });
            
            // 创建占位图函数
            function createPlaceholder(img) {
                const canvas = document.createElement('canvas');
                canvas.width = 100;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#f0f0f0';
                ctx.fillRect(0, 0, 100, 100);
                ctx.fillStyle = '#666';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(img.alt || '图片', 50, 50);
                img.src = canvas.toDataURL();
            }
            
            // 截图并下载函数
            function captureAndDownload() {
                statusDiv.textContent = '生成截图中...';
                
                html2canvas(clonedContainer, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: false
                }).then(canvas => {
                    statusDiv.textContent = '截图生成成功，准备下载...';
                    
                    // 创建下载链接
                    const imgData = canvas.toDataURL('image/png');
                    const link = document.createElement('a');
                    link.download = '张伟_简历.png';
                    link.href = imgData;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    // 清理
                    document.body.removeChild(clonedContainer);
                    statusDiv.textContent = '下载已开始...';
                    setTimeout(() => {
                        document.body.removeChild(statusDiv);
                    }, 2000);
                    
                    // 恢复按钮状态
                    button.disabled = false;
                    button.innerHTML = '<i class="bi bi-file-earmark-image"></i> 下载简历 (图片)';
                    if (downloadSection) downloadSection.style.display = 'flex';
                }).catch(err => {
                    console.error('生成截图时出错:', err);
                    alert('生成截图时出错: ' + err.message);
                    
                    // 清理
                    document.body.removeChild(clonedContainer);
                    document.body.removeChild(statusDiv);
                    
                    // 恢复按钮状态
                    button.disabled = false;
                    button.innerHTML = '<i class="bi bi-file-earmark-image"></i> 下载简历 (图片)';
                    if (downloadSection) downloadSection.style.display = 'flex';
                });
            }
        });

        // PDF下载功能
        document.getElementById('downloadPdfButton').addEventListener('click', function() {
            const resumeContainer = document.querySelector('.container');
            const button = this;
            const downloadSection = document.querySelector('.download-section');
            
            if (downloadSection) downloadSection.style.display = 'none';
            
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';
            
            // 创建一个简单的状态指示器
            const statusDiv = document.createElement('div');
            statusDiv.style.position = 'fixed';
            statusDiv.style.bottom = '20px';
            statusDiv.style.left = '20px';
            statusDiv.style.padding = '10px';
            statusDiv.style.backgroundColor = 'rgba(0,0,0,0.7)';
            statusDiv.style.color = 'white';
            statusDiv.style.borderRadius = '5px';
            statusDiv.style.zIndex = '9999';
            statusDiv.textContent = '正在准备PDF...';
            document.body.appendChild(statusDiv);
            
            // 获取容器尺寸
            const containerWidth = resumeContainer.offsetWidth;
            const containerHeight = resumeContainer.offsetHeight;
            
            // 创建一个临时的div来容纳简历内容
            const tempDiv = document.createElement('div');
            tempDiv.style.width = containerWidth + 'px';
            tempDiv.style.padding = '40px';
            tempDiv.style.backgroundColor = '#fff';
            tempDiv.style.position = 'absolute';
            tempDiv.style.left = '-9999px';
            tempDiv.style.top = '0';
            tempDiv.innerHTML = resumeContainer.innerHTML;
            document.body.appendChild(tempDiv);
            
            // 先预处理所有图片，将它们转换为base64格式
            preprocessImages(tempDiv).then(() => {
                statusDiv.textContent = '正在生成PDF...';
                
                // 使用html2canvas直接处理
                html2canvas(tempDiv, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: true
                }).then(function(canvas) {
                    statusDiv.textContent = 'PDF生成中...';
                    console.log('Canvas生成成功，尺寸:', canvas.width, 'x', canvas.height);
                    
                    try {
                        const { jsPDF } = window.jspdf;
                        
                        // 创建PDF文档
                        const pdf = new jsPDF({
                            orientation: 'p',
                            unit: 'mm',
                            format: 'a4',
                            compress: true
                        });
                        
                        // 获取A4尺寸
                        const pageWidth = pdf.internal.pageSize.getWidth();
                        const pageHeight = pdf.internal.pageSize.getHeight();
                        
                        // 获取图像数据
                        const imgData = canvas.toDataURL('image/jpeg', 0.95);
                        console.log('图片数据生成成功，长度:', imgData.length);
                        
                        // 计算图像尺寸以适合页面
                        const imgWidth = pageWidth - 20; // 左右各留10mm边距
                        const imgHeight = (canvas.height * imgWidth) / canvas.width;
                        
                        // 添加图像到PDF
                        pdf.addImage(imgData, 'JPEG', 10, 10, imgWidth, imgHeight);
                        
                        // 如果内容超过一页，添加更多页面
                        if (imgHeight > pageHeight - 20) {
                            let heightLeft = imgHeight - (pageHeight - 20);
                            let position = -(pageHeight - 20);
                            let pageNum = 1;
                            
                            while (heightLeft > 0) {
                                pdf.addPage();
                                pageNum++;
                                pdf.addImage(imgData, 'JPEG', 10, position, imgWidth, imgHeight);
                                heightLeft -= (pageHeight - 20);
                                position -= (pageHeight - 20);
                            }
                        }
                        
                        // 生成PDF数据
                        const pdfData = pdf.output('blob');
                        
                        // 创建一个下载链接
                        const pdfUrl = URL.createObjectURL(pdfData);
                        const link = document.createElement('a');
                        link.href = pdfUrl;
                        link.download = '张伟_简历.pdf';
                        link.style.display = 'none';
                        document.body.appendChild(link);
                        link.click();
                        
                        // 清理
                        setTimeout(function() {
                            document.body.removeChild(link);
                            URL.revokeObjectURL(pdfUrl);
                        }, 100);
                        
                        statusDiv.textContent = 'PDF下载中...';
                        setTimeout(function() {
                            document.body.removeChild(statusDiv);
                        }, 2000);
                    } catch (err) {
                        console.error('PDF处理错误:', err);
                        alert('生成PDF时出现问题: ' + err.message);
                        document.body.removeChild(statusDiv);
                    }
                    
                    // 移除临时元素
                    document.body.removeChild(tempDiv);
                    
                    // 恢复按钮状态
                    button.disabled = false;
                    button.innerHTML = '<i class="bi bi-file-earmark-pdf"></i> 下载简历 (PDF)';
                    
                    if (downloadSection) downloadSection.style.display = 'flex';
                }).catch(function(err) {
                    console.error('生成canvas失败:', err);
                    alert('生成PDF失败: ' + err.message);
                    
                    // 移除临时元素
                    document.body.removeChild(tempDiv);
                    document.body.removeChild(statusDiv);
                    
                    // 恢复按钮状态
                    button.disabled = false;
                    button.innerHTML = '<i class="bi bi-file-earmark-pdf"></i> 下载简历 (PDF)';
                    if (downloadSection) downloadSection.style.display = 'flex';
                });
            }).catch(function(err) {
                console.error('预处理图片失败:', err);
                alert('预处理图片失败: ' + err.message);
                
                // 移除临时元素和状态指示器
                document.body.removeChild(tempDiv);
                document.body.removeChild(statusDiv);
                
                // 恢复按钮状态
                button.disabled = false;
                button.innerHTML = '<i class="bi bi-file-earmark-pdf"></i> 下载简历 (PDF)';
                if (downloadSection) downloadSection.style.display = 'flex';
            });
        });
    </script>
</body>
</html> 

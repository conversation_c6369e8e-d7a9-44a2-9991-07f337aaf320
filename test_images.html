<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .image-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
        .image-item {
            text-align: center;
        }
        .image-item img {
            max-width: 150px;
            max-height: 150px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .image-item p {
            margin: 5px 0;
            font-size: 12px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>图片加载测试</h1>
    
    <div class="test-section">
        <h2>当前图片状态</h2>
        <div class="image-container">
            <div class="image-item">
                <img src="1.png" alt="个人照片">
                <p>1.png</p>
            </div>
            <div class="image-item">
                <img src="人工智能编程.png" alt="人工智能编程">
                <p>人工智能编程.png</p>
            </div>
            <div class="image-item">
                <img src="缘分黔城.jpg" alt="缘分黔城">
                <p>缘分黔城.jpg</p>
            </div>
            <div class="image-item">
                <img src="智叶科技服务.jpg" alt="智叶科技服务">
                <p>智叶科技服务.jpg</p>
            </div>
        </div>
        
        <button onclick="checkImages()">检查图片状态</button>
        <button onclick="testImageConversion()">测试图片转换</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="test-section">
        <h2>日志输出</h2>
        <div id="log"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function checkImages() {
            log('开始检查图片状态...');
            const images = document.querySelectorAll('img');
            
            images.forEach((img, index) => {
                log(`图片 ${index + 1}:`);
                log(`  - src: ${img.src}`);
                log(`  - naturalWidth: ${img.naturalWidth}`);
                log(`  - naturalHeight: ${img.naturalHeight}`);
                log(`  - complete: ${img.complete}`);
                log(`  - 是否为data URI: ${img.src.startsWith('data:')}`);
                
                if (img.complete && img.naturalWidth > 0) {
                    log(`  - 状态: ✅ 加载成功`);
                } else {
                    log(`  - 状态: ❌ 加载失败或未完成`);
                }
                log('');
            });
        }

        function testImageConversion() {
            log('开始测试图片转换...');
            const images = document.querySelectorAll('img');
            let processedCount = 0;
            
            images.forEach((img, index) => {
                if (img.src.startsWith('data:')) {
                    log(`图片 ${index + 1} 已经是 data URI，跳过`);
                    processedCount++;
                    if (processedCount === images.length) {
                        log('所有图片处理完成');
                    }
                    return;
                }
                
                const newImg = new Image();
                
                newImg.onload = function() {
                    try {
                        const canvas = document.createElement('canvas');
                        canvas.width = newImg.naturalWidth || newImg.width || 100;
                        canvas.height = newImg.naturalHeight || newImg.height || 100;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(newImg, 0, 0, canvas.width, canvas.height);
                        const dataURL = canvas.toDataURL('image/png');
                        
                        log(`图片 ${index + 1} 转换成功: ${dataURL.length} 字符`);
                        // 可选：替换原图片
                        // img.src = dataURL;
                    } catch (e) {
                        log(`图片 ${index + 1} 转换失败: ${e.message}`);
                    }
                    
                    processedCount++;
                    if (processedCount === images.length) {
                        log('所有图片处理完成');
                    }
                };
                
                newImg.onerror = function() {
                    log(`图片 ${index + 1} 加载失败: ${img.src}`);
                    processedCount++;
                    if (processedCount === images.length) {
                        log('所有图片处理完成');
                    }
                };
                
                newImg.src = img.src;
            });
        }

        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            log('页面加载完成，自动检查图片状态...');
            setTimeout(checkImages, 1000);
        });
    </script>
</body>
</html>

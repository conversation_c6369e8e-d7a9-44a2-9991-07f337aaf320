<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载功能调试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            width: 400px;
            padding: 20px;
            border: 1px solid #ccc;
            background: white;
            margin: 20px 0;
        }
        .test-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            margin: 10px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>下载功能调试</h1>
    
    <div class="test-container" id="testContainer">
        <h2>测试内容</h2>
        <p>这是一个测试容器，包含文字和图片</p>
        <img src="1.png" alt="测试图片1" class="test-image">
        <img src="人工智能编程.png" alt="测试图片2" class="test-image">
        <p>更多文字内容...</p>
    </div>
    
    <button onclick="testSimpleCapture()">简单截图测试</button>
    <button onclick="testWithImagePreprocess()">预处理图片后截图</button>
    <button onclick="clearLog()">清空日志</button>
    
    <div id="log"></div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testSimpleCapture() {
            log('开始简单截图测试...');
            const container = document.getElementById('testContainer');
            
            html2canvas(container, {
                scale: 1,
                useCORS: false,
                allowTaint: true,
                backgroundColor: '#ffffff',
                logging: true,
                foreignObjectRendering: false
            }).then(canvas => {
                log(`截图成功! 尺寸: ${canvas.width}x${canvas.height}`);
                
                // 创建下载链接
                const imgData = canvas.toDataURL('image/png');
                const link = document.createElement('a');
                link.download = '简单截图测试.png';
                link.href = imgData;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                log('下载已开始');
            }).catch(err => {
                log(`截图失败: ${err.message}`);
                console.error('截图错误:', err);
            });
        }

        function preprocessImages(container) {
            log('开始预处理图片...');
            return new Promise((resolve) => {
                const images = container.querySelectorAll('img');
                let processedCount = 0;

                if (images.length === 0) {
                    log('没有找到图片');
                    resolve();
                    return;
                }
                
                log(`找到 ${images.length} 个图片`);

                images.forEach((img, index) => {
                    log(`处理图片 ${index + 1}: ${img.src}`);
                    
                    if (img.src.startsWith('data:')) {
                        log(`图片 ${index + 1} 已经是 data URI，跳过`);
                        processedCount++;
                        if (processedCount === images.length) {
                            log('所有图片处理完成');
                            resolve();
                        }
                        return;
                    }

                    const newImg = new Image();
                    
                    newImg.onload = function() {
                        log(`图片 ${index + 1} 加载成功: ${newImg.naturalWidth}x${newImg.naturalHeight}`);
                        try {
                            const canvas = document.createElement('canvas');
                            canvas.width = newImg.naturalWidth || newImg.width || 100;
                            canvas.height = newImg.naturalHeight || newImg.height || 100;
                            const ctx = canvas.getContext('2d');
                            ctx.drawImage(newImg, 0, 0, canvas.width, canvas.height);
                            const dataURL = canvas.toDataURL('image/png');
                            img.src = dataURL;
                            log(`图片 ${index + 1} 转换为 data URI 成功`);
                        } catch (e) {
                            log(`图片 ${index + 1} 转换失败: ${e.message}`);
                        }
                        
                        processedCount++;
                        if (processedCount === images.length) {
                            log('所有图片处理完成');
                            resolve();
                        }
                    };
                    
                    newImg.onerror = function() {
                        log(`图片 ${index + 1} 加载失败`);
                        processedCount++;
                        if (processedCount === images.length) {
                            log('所有图片处理完成（部分失败）');
                            resolve();
                        }
                    };
                    
                    newImg.src = img.src;
                });
            });
        }

        function testWithImagePreprocess() {
            log('开始预处理图片后截图测试...');
            const container = document.getElementById('testContainer');
            
            // 创建容器的克隆
            const clonedContainer = container.cloneNode(true);
            clonedContainer.style.position = 'absolute';
            clonedContainer.style.left = '-9999px';
            clonedContainer.style.top = '0';
            document.body.appendChild(clonedContainer);
            
            preprocessImages(clonedContainer).then(() => {
                log('图片预处理完成，开始截图...');
                
                html2canvas(clonedContainer, {
                    scale: 1,
                    useCORS: false,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: true,
                    foreignObjectRendering: false
                }).then(canvas => {
                    log(`截图成功! 尺寸: ${canvas.width}x${canvas.height}`);
                    
                    // 创建下载链接
                    const imgData = canvas.toDataURL('image/png');
                    const link = document.createElement('a');
                    link.download = '预处理后截图测试.png';
                    link.href = imgData;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    // 清理
                    document.body.removeChild(clonedContainer);
                    log('下载已开始');
                }).catch(err => {
                    log(`截图失败: ${err.message}`);
                    console.error('截图错误:', err);
                    document.body.removeChild(clonedContainer);
                });
            }).catch(err => {
                log(`图片预处理失败: ${err.message}`);
                document.body.removeChild(clonedContainer);
            });
        }
    </script>
</body>
</html>
